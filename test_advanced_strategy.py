#!/usr/bin/env python3
"""
Simple test for the advanced flying strategy without full dependencies
"""

import sys
import os
import math
import numpy as np

# Add the project root to Python path
sys.path.insert(0, '/Users/<USER>/Downloads/swarm-main')

def test_imports():
    """Test that all imports work correctly"""
    try:
        # Test our advanced flying strategy imports
        from swarm.core.advanced_flying_strategy import (
            Obstacle, PathNode, AdvancedPathPlanner,
            optimize_waypoints, smooth_trajectory
        )
        print("✓ Advanced flying strategy imports successful")
        return True
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False
    except Exception as e:
        print(f"✗ Unexpected error: {e}")
        return False

def test_obstacle_creation():
    """Test Obstacle class"""
    try:
        from swarm.core.advanced_flying_strategy import Obstacle
        
        obs = Obstacle(x=1.0, y=2.0, z=3.0, radius=0.5, height=2.0, shape_type='box')
        print(f"✓ Obstacle created: pos=({obs.x}, {obs.y}, {obs.z}), radius={obs.radius}")
        
        return True
        
    except Exception as e:
        print(f"✗ Obstacle creation error: {e}")
        return False

def test_pathnode_functionality():
    """Test PathNode class and comparison"""
    try:
        from swarm.core.advanced_flying_strategy import PathNode
        
        node1 = PathNode(x=1.0, y=2.0, z=3.0, g_cost=5.0, f_cost=10.0)
        node2 = PathNode(x=1.0, y=2.0, z=3.0, g_cost=7.0, f_cost=12.0)
        node3 = PathNode(x=2.0, y=3.0, z=4.0, g_cost=3.0, f_cost=8.0)
        
        # Test equality (should be equal based on position, not cost)
        equal_nodes = node1 == node2
        different_nodes = node1 == node3
        print(f"✓ Node equality test: same position = {equal_nodes}, different position = {different_nodes}")
        
        # Test less than comparison (for heapq)
        comparison = node1 < node3
        print(f"✓ Node comparison test: node1 < node3 = {comparison}")
        
        # Test hashing
        node_set = {node1, node2, node3}
        print(f"✓ Set of nodes has {len(node_set)} unique elements")
        
        return True
        
    except Exception as e:
        print(f"✗ PathNode functionality error: {e}")
        return False

def test_path_planner():
    """Test AdvancedPathPlanner creation and basic functionality"""
    try:
        from swarm.core.advanced_flying_strategy import AdvancedPathPlanner, Obstacle
        
        # Create some test obstacles
        obstacles = [
            Obstacle(x=5.0, y=5.0, z=2.0, radius=1.0, height=3.0, shape_type='box'),
            Obstacle(x=10.0, y=8.0, z=2.0, radius=1.5, height=4.0, shape_type='cylinder'),
        ]
        
        # Create path planner
        world_bounds = (-15, 15, -15, 15)
        planner = AdvancedPathPlanner(obstacles, world_bounds)
        print(f"✓ AdvancedPathPlanner created with {len(planner.obstacles)} obstacles")
        print(f"✓ Obstacle grid has {len(planner.obstacle_grid)} occupied cells")
        
        # Test position validation
        valid_pos = planner._is_valid_position(0.0, 0.0, 5.0)
        invalid_pos = planner._is_valid_position(5.0, 5.0, 2.0)  # Should be in obstacle
        print(f"✓ Position validation: clear area = {valid_pos}, obstacle area = {invalid_pos}")
        
        return True
        
    except Exception as e:
        print(f"✗ Path planner error: {e}")
        return False

def test_helper_functions():
    """Test helper functions"""
    try:
        from swarm.core.advanced_flying_strategy import optimize_waypoints, smooth_trajectory, Obstacle
        
        # Test waypoint optimization
        path = [(0, 0, 2), (1, 1, 2), (2, 2, 2), (3, 3, 2), (4, 4, 2)]
        obstacles = []
        optimized = optimize_waypoints(path, obstacles)
        print(f"✓ Waypoint optimization: {len(path)} -> {len(optimized)} points")
        print(f"  Original: {path}")
        print(f"  Optimized: {optimized}")
        
        # Test trajectory smoothing
        smoothed = smooth_trajectory(path)
        print(f"✓ Trajectory smoothing: {len(path)} -> {len(smoothed)} points")
        
        return True
        
    except Exception as e:
        print(f"✗ Helper function error: {e}")
        return False

def test_path_finding():
    """Test basic path finding functionality"""
    try:
        from swarm.core.advanced_flying_strategy import AdvancedPathPlanner, Obstacle
        
        # Create a simple scenario with one obstacle
        obstacles = [
            Obstacle(x=5.0, y=5.0, z=3.0, radius=2.0, height=4.0, shape_type='box'),
        ]
        
        world_bounds = (-10, 10, -10, 10)
        planner = AdvancedPathPlanner(obstacles, world_bounds)
        
        # Try to find a path from start to goal
        start = (0.0, 0.0, 3.0)
        goal = (10.0, 10.0, 3.0)
        
        print(f"✓ Attempting path finding from {start} to {goal}")
        path = planner.find_path(start, goal)
        
        if path:
            print(f"✓ Path found with {len(path)} waypoints")
            print(f"  Start: {path[0]}")
            print(f"  End: {path[-1]}")
            if len(path) > 2:
                print(f"  Sample intermediate: {path[len(path)//2]}")
        else:
            print("✓ No path found (expected for complex scenarios)")
        
        return True
        
    except Exception as e:
        print(f"✗ Path finding error: {e}")
        return False

def main():
    """Run all tests"""
    print("Testing Advanced Flying Strategy Implementation")
    print("=" * 50)
    
    tests = [
        ("Import Tests", test_imports),
        ("Obstacle Creation", test_obstacle_creation),
        ("PathNode Functionality", test_pathnode_functionality),
        ("Path Planner Creation", test_path_planner),
        ("Helper Functions", test_helper_functions),
        ("Path Finding", test_path_finding),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 30)
        try:
            if test_func():
                print(f"✓ {test_name} PASSED")
                passed += 1
            else:
                print(f"✗ {test_name} FAILED")
        except Exception as e:
            print(f"✗ {test_name} FAILED with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The advanced flying strategy is working correctly.")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

# Wandb Configuration for Swarm Subnet Validator
# Copy this file to .env and set your values

# Wandb API Key (distributed separately to validators)
# API key
WANDB_API_KEY=your_wandb_api_key_here

# Optional: Custom validator name (defaults to validator-{uid})
VALIDATOR_NAME=validator-custom-name

# Project settings (already configured in code)
# PROJECT: validator-logs
# ENTITY: swarm-subnet-swarm
# URL: https://wandb.ai/swarm-subnet-swarm/validator-logs

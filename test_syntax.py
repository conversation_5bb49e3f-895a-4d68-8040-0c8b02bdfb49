#!/usr/bin/env python3
"""
Simple syntax test for the advanced flying strategy
"""

import sys
import os

# Add the project root to Python path
sys.path.insert(0, '/Users/<USER>/Downloads/swarm-main')

def test_imports():
    """Test that all imports work correctly"""
    try:
        # Test basic imports
        import numpy as np
        print("✓ numpy import successful")
        
        # Test our advanced flying strategy imports
        from swarm.core.advanced_flying_strategy import (
            Obstacle, PathNode, AdvancedPathPlanner,
            extract_obstacles_from_environment, optimize_waypoints,
            smooth_trajectory, advanced_flying_strategy
        )
        print("✓ Advanced flying strategy imports successful")
        
        # Test protocol imports
        from swarm.protocol import MapTask, RPMCmd, FlightPlan
        print("✓ Protocol imports successful")
        
        return True
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False
    except Exception as e:
        print(f"✗ Unexpected error: {e}")
        return False

def test_class_instantiation():
    """Test that classes can be instantiated"""
    try:
        # Test Obstacle creation
        obs = Obstacle(x=1.0, y=2.0, z=3.0, radius=0.5, height=2.0, shape_type='box')
        print(f"✓ Obstacle created: {obs}")
        
        # Test PathNode creation
        node = PathNode(x=0.0, y=0.0, z=2.0)
        print(f"✓ PathNode created: {node}")
        
        # Test AdvancedPathPlanner creation
        obstacles = [obs]
        world_bounds = (-30, 30, -30, 30)
        planner = AdvancedPathPlanner(obstacles, world_bounds)
        print(f"✓ AdvancedPathPlanner created with {len(planner.obstacles)} obstacles")
        
        return True
        
    except Exception as e:
        print(f"✗ Class instantiation error: {e}")
        return False

def test_pathnode_comparison():
    """Test PathNode comparison and hashing"""
    try:
        node1 = PathNode(x=1.0, y=2.0, z=3.0, g_cost=5.0, f_cost=10.0)
        node2 = PathNode(x=1.0, y=2.0, z=3.0, g_cost=7.0, f_cost=12.0)
        node3 = PathNode(x=2.0, y=3.0, z=4.0, g_cost=3.0, f_cost=8.0)
        
        # Test equality (should be equal based on position, not cost)
        print(f"✓ Node1 == Node2: {node1 == node2}")
        print(f"✓ Node1 == Node3: {node1 == node3}")
        
        # Test less than comparison (for heapq)
        print(f"✓ Node1 < Node3: {node1 < node3}")
        print(f"✓ Node3 < Node1: {node3 < node1}")
        
        # Test hashing
        node_set = {node1, node2, node3}
        print(f"✓ Set of nodes has {len(node_set)} unique elements")
        
        return True
        
    except Exception as e:
        print(f"✗ PathNode comparison error: {e}")
        return False

def test_helper_functions():
    """Test helper functions"""
    try:
        # Test waypoint optimization
        path = [(0, 0, 2), (1, 1, 2), (2, 2, 2), (3, 3, 2)]
        obstacles = []
        optimized = optimize_waypoints(path, obstacles)
        print(f"✓ Waypoint optimization: {len(path)} -> {len(optimized)} points")
        
        # Test trajectory smoothing
        smoothed = smooth_trajectory(path)
        print(f"✓ Trajectory smoothing: {len(path)} -> {len(smoothed)} points")
        
        return True
        
    except Exception as e:
        print(f"✗ Helper function error: {e}")
        return False

def main():
    """Run all syntax tests"""
    print("Running Advanced Flying Strategy Syntax Tests")
    print("=" * 50)
    
    tests = [
        ("Import Tests", test_imports),
        ("Class Instantiation Tests", test_class_instantiation),
        ("PathNode Comparison Tests", test_pathnode_comparison),
        ("Helper Function Tests", test_helper_functions),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 30)
        if test_func():
            print(f"✓ {test_name} PASSED")
            passed += 1
        else:
            print(f"✗ {test_name} FAILED")
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All syntax tests passed! The advanced flying strategy is ready.")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

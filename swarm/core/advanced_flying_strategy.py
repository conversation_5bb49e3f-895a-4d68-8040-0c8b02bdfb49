
from __future__ import annotations

import time
import math
import heapq
from typing import List, Sequence, Tuple, Optional, Set, Dict
from dataclasses import dataclass

import numpy as np
import pybullet as p
from gym_pybullet_drones.utils.enums import ObservationType, ActionType

from swarm.utils.gui_isolation import run_isolated
from swarm.utils.env_factory import make_env
from swarm.protocol import MapTask, RPMCmd
from swarm.core.drone import track_drone


from swarm.constants import (
    SAFE_Z,
    GOAL_TOL,
    HOVER_SEC,
    CAM_HZ,
    LANDING_PLATFORM_RADIUS,
    STABLE_LANDING_SEC,
    PLATFORM,
    WORLD_RANGE,
)

GRID_RESOLUTION = 0.8     
SAFETY_MARGIN = 1.2        
MAX_PLANNING_TIME = 3.0    
WAYPOINT_SPACING = 1.5     
ENERGY_WEIGHT = 0.3        
TIME_WEIGHT = 0.7          
MIN_FLIGHT_HEIGHT = 1.5    
MAX_FLIGHT_HEIGHT = 15.0   


@dataclass
class Obstacle:
    x: float
    y: float
    z: float
    radius: float
    height: float
    shape_type: str

@dataclass
class PathNode:
    x: float
    y: float
    z: float
    g_cost: float = float('inf')
    h_cost: float = 0.0
    f_cost: float = float('inf')
    parent: Optional['PathNode'] = None

    def __hash__(self):
        return hash((round(self.x/GRID_RESOLUTION), round(self.y/GRID_RESOLUTION), round(self.z/GRID_RESOLUTION)))

    def __eq__(self, other):
        if not isinstance(other, PathNode):
            return False
        return (abs(self.x - other.x) < GRID_RESOLUTION/2 and
                abs(self.y - other.y) < GRID_RESOLUTION/2 and
                abs(self.z - other.z) < GRID_RESOLUTION/2)

    def __lt__(self, other):
        if not isinstance(other, PathNode):
            return NotImplemented
        return self.f_cost < other.f_cost

class AdvancedPathPlanner:

    def __init__(self, obstacles: List[Obstacle], world_bounds: Tuple[float, float, float, float]):
        self.obstacles = obstacles
        self.x_min, self.x_max, self.y_min, self.y_max = world_bounds
        self.obstacle_grid = self._build_obstacle_grid()

    def _build_obstacle_grid(self) -> Set[Tuple[int, int, int]]:
        occupied = set()

        for obs in self.obstacles:
            x_start = int((obs.x - obs.radius - SAFETY_MARGIN) / GRID_RESOLUTION)
            x_end = int((obs.x + obs.radius + SAFETY_MARGIN) / GRID_RESOLUTION) + 1
            y_start = int((obs.y - obs.radius - SAFETY_MARGIN) / GRID_RESOLUTION)
            y_end = int((obs.y + obs.radius + SAFETY_MARGIN) / GRID_RESOLUTION) + 1
            z_start = int((obs.z - obs.height/2) / GRID_RESOLUTION)
            z_end = int((obs.z + obs.height/2 + SAFETY_MARGIN) / GRID_RESOLUTION) + 1

            for x_idx in range(x_start, x_end):
                for y_idx in range(y_start, y_end):
                    for z_idx in range(z_start, z_end):
                        x_world = x_idx * GRID_RESOLUTION
                        y_world = y_idx * GRID_RESOLUTION
                        z_world = z_idx * GRID_RESOLUTION

                        if self._point_in_obstacle(x_world, y_world, z_world, obs):
                            occupied.add((x_idx, y_idx, z_idx))

        return occupied
    
    def _point_in_obstacle(self, x: float, y: float, z: float, obs: Obstacle) -> bool:
        horiz_dist = math.sqrt((x - obs.x)**2 + (y - obs.y)**2)
        if horiz_dist > obs.radius + SAFETY_MARGIN:
            return False

        if z < obs.z - obs.height/2 or z > obs.z + obs.height/2 + SAFETY_MARGIN:
            return False

        return True

    def _is_valid_position(self, x: float, y: float, z: float) -> bool:
        if (x < self.x_min or x > self.x_max or
            y < self.y_min or y > self.y_max or
            z < MIN_FLIGHT_HEIGHT or z > MAX_FLIGHT_HEIGHT):
            return False

        x_idx = int(x / GRID_RESOLUTION)
        y_idx = int(y / GRID_RESOLUTION)
        z_idx = int(z / GRID_RESOLUTION)

        return (x_idx, y_idx, z_idx) not in self.obstacle_grid

    def _heuristic(self, node: PathNode, goal: PathNode) -> float:
        dx = node.x - goal.x
        dy = node.y - goal.y
        dz = node.z - goal.z

        distance = math.sqrt(dx*dx + dy*dy + dz*dz)

        altitude_penalty = abs(dz) * 0.5

        return distance + altitude_penalty
    
    def _get_neighbors(self, node: PathNode) -> List[PathNode]:
        neighbors = []

        directions = [
            (1, 0, 0), (-1, 0, 0), (0, 1, 0), (0, -1, 0), (0, 0, 1), (0, 0, -1),
            (1, 1, 0), (1, -1, 0), (-1, 1, 0), (-1, -1, 0),
            (1, 0, 1), (1, 0, -1), (-1, 0, 1), (-1, 0, -1),
            (0, 1, 1), (0, 1, -1), (0, -1, 1), (0, -1, -1),
            (1, 1, 1), (1, 1, -1), (1, -1, 1), (1, -1, -1),
            (-1, 1, 1), (-1, 1, -1), (-1, -1, 1), (-1, -1, -1)
        ]

        for dx, dy, dz in directions:
            new_x = node.x + dx * GRID_RESOLUTION
            new_y = node.y + dy * GRID_RESOLUTION
            new_z = node.z + dz * GRID_RESOLUTION

            if self._is_valid_position(new_x, new_y, new_z):
                neighbors.append(PathNode(new_x, new_y, new_z))

        return neighbors

    def _calculate_movement_cost(self, from_node: PathNode, to_node: PathNode) -> float:
        dx = to_node.x - from_node.x
        dy = to_node.y - from_node.y
        dz = to_node.z - from_node.z

        distance = math.sqrt(dx*dx + dy*dy + dz*dz)

        if dz > 0:
            energy_penalty = dz * 2.0
        else:
            energy_penalty = abs(dz) * 0.5

        time_penalty = distance * 1.0

        return TIME_WEIGHT * time_penalty + ENERGY_WEIGHT * energy_penalty
    
    def find_path(self, start: Tuple[float, float, float],
                  goal: Tuple[float, float, float]) -> Optional[List[Tuple[float, float, float]]]:
        start_node = PathNode(start[0], start[1], start[2], g_cost=0.0)
        goal_node = PathNode(goal[0], goal[1], goal[2])

        start_node.h_cost = self._heuristic(start_node, goal_node)
        start_node.f_cost = start_node.g_cost + start_node.h_cost

        open_set = [start_node]
        closed_set: Set[PathNode] = set()
        node_map: Dict[Tuple[int, int, int], PathNode] = {}

        start_time = time.time()

        while open_set and (time.time() - start_time) < MAX_PLANNING_TIME:
            current = heapq.heappop(open_set)

            if (abs(current.x - goal_node.x) < GRID_RESOLUTION and
                abs(current.y - goal_node.y) < GRID_RESOLUTION and
                abs(current.z - goal_node.z) < GRID_RESOLUTION):
                path = []
                while current:
                    path.append((current.x, current.y, current.z))
                    current = current.parent
                return path[::-1]

            closed_set.add(current)

            for neighbor in self._get_neighbors(current):
                if neighbor in closed_set:
                    continue

                tentative_g = current.g_cost + self._calculate_movement_cost(current, neighbor)

                neighbor_key = (int(neighbor.x/GRID_RESOLUTION),
                               int(neighbor.y/GRID_RESOLUTION),
                               int(neighbor.z/GRID_RESOLUTION))

                if neighbor_key in node_map:
                    existing_neighbor = node_map[neighbor_key]
                    if tentative_g >= existing_neighbor.g_cost:
                        continue
                    neighbor = existing_neighbor
                else:
                    node_map[neighbor_key] = neighbor

                neighbor.g_cost = tentative_g
                neighbor.h_cost = self._heuristic(neighbor, goal_node)
                neighbor.f_cost = neighbor.g_cost + neighbor.h_cost
                neighbor.parent = current

                if neighbor not in open_set:
                    heapq.heappush(open_set, neighbor)

        return None


def extract_obstacles_from_environment(cli: int) -> List[Obstacle]:
    obstacles = []

    num_bodies = p.getNumBodies(physicsClientId=cli)

    for body_id in range(num_bodies):
        if body_id <= 1:
            continue

        try:
            pos, _ = p.getBasePositionAndOrientation(body_id, physicsClientId=cli)

            collision_info = p.getCollisionShapeData(body_id, -1, physicsClientId=cli)

            if not collision_info:
                continue

            for shape_data in collision_info:
                shape_type = shape_data[2]
                dimensions = shape_data[3]
                local_pos = shape_data[5]

                world_x = pos[0] + local_pos[0]
                world_y = pos[1] + local_pos[1]
                world_z = pos[2] + local_pos[2]

                if shape_type == p.GEOM_BOX:
                    radius = math.sqrt(dimensions[0]**2 + dimensions[1]**2)
                    height = dimensions[2] * 2
                    obstacles.append(Obstacle(world_x, world_y, world_z, radius, height, 'box'))

                elif shape_type == p.GEOM_CYLINDER:
                    radius = dimensions[0]
                    height = dimensions[1]
                    obstacles.append(Obstacle(world_x, world_y, world_z, radius, height, 'cylinder'))

                elif shape_type == p.GEOM_SPHERE:
                    radius = dimensions[0]
                    height = radius * 2
                    obstacles.append(Obstacle(world_x, world_y, world_z, radius, height, 'sphere'))

        except Exception:
            continue

    return obstacles


def optimize_waypoints(path: List[Tuple[float, float, float]],
                      obstacles: List[Obstacle]) -> List[Tuple[float, float, float]]:
    if len(path) <= 2:
        return path

    optimized = [path[0]]

    i = 0
    while i < len(path) - 1:
        farthest_reachable = i + 1

        for j in range(i + 2, len(path)):
            if _is_line_clear(path[i], path[j], obstacles):
                farthest_reachable = j
            else:
                break

        if farthest_reachable < len(path):
            optimized.append(path[farthest_reachable])

        i = farthest_reachable

    if optimized[-1] != path[-1]:
        optimized.append(path[-1])

    return optimized


def _is_line_clear(start: Tuple[float, float, float],
                   end: Tuple[float, float, float],
                   obstacles: List[Obstacle]) -> bool:
    num_samples = int(np.linalg.norm(np.array(end) - np.array(start)) / (GRID_RESOLUTION / 2)) + 1

    for i in range(num_samples + 1):
        t = i / num_samples if num_samples > 0 else 0
        point = (
            start[0] + t * (end[0] - start[0]),
            start[1] + t * (end[1] - start[1]),
            start[2] + t * (end[2] - start[2])
        )

        for obs in obstacles:
            if _point_in_obstacle_simple(point[0], point[1], point[2], obs):
                return False

    return True


def _point_in_obstacle_simple(x: float, y: float, z: float, obs: Obstacle) -> bool:
    horiz_dist = math.sqrt((x - obs.x)**2 + (y - obs.y)**2)
    if horiz_dist > obs.radius + SAFETY_MARGIN:
        return False

    if z < obs.z - obs.height/2 or z > obs.z + obs.height/2 + SAFETY_MARGIN:
        return False

    return True


def smooth_trajectory(waypoints: List[Tuple[float, float, float]],
                     smoothing_factor: float = 0.3) -> List[Tuple[float, float, float]]:
    if len(waypoints) <= 2:
        return waypoints

    smoothed = [waypoints[0]]

    for i in range(1, len(waypoints) - 1):
        prev_wp = np.array(waypoints[i-1])
        curr_wp = np.array(waypoints[i])
        next_wp = np.array(waypoints[i+1])

        smoothed_point = (
            (1 - smoothing_factor) * curr_wp +
            smoothing_factor * 0.5 * (prev_wp + next_wp)
        )

        smoothed.append(tuple(smoothed_point))

    smoothed.append(waypoints[-1])
    return smoothed


def advanced_flying_strategy(task: MapTask, *, gui: bool = False) -> List[RPMCmd]:
    return run_isolated(_advanced_flying_strategy_impl, task, gui=gui)


def _advanced_flying_strategy_impl(task: MapTask, *, gui: bool = False) -> List[RPMCmd]:
    env = make_env(task, gui=gui, raw_rpm=False)
    cli = env.getPyBulletClient()

    obstacles = extract_obstacles_from_environment(cli)

    start_xyz = np.array(task.start, dtype=float)
    goal_xyz = np.array(task.goal, dtype=float)

    safe_z = max(SAFE_Z, start_xyz[2], goal_xyz[2])

    world_bounds = (-WORLD_RANGE, WORLD_RANGE, -WORLD_RANGE, WORLD_RANGE)
    planner = AdvancedPathPlanner(obstacles, world_bounds)

    start_point = (start_xyz[0], start_xyz[1], safe_z)
    intermediate_point = (goal_xyz[0], goal_xyz[1], safe_z)
    final_point = (goal_xyz[0], goal_xyz[1], goal_xyz[2])

    path_to_intermediate = planner.find_path(start_point, intermediate_point)

    path_to_goal = planner.find_path(intermediate_point, final_point)

    if path_to_intermediate and path_to_goal:
        full_path = path_to_intermediate + path_to_goal[1:]
    elif path_to_intermediate:
        full_path = path_to_intermediate + [final_point]
    else:
        full_path = [start_point, intermediate_point, final_point]

    optimized_path = optimize_waypoints(full_path, obstacles)
    smooth_path = smooth_trajectory(optimized_path)

    return _execute_flight_plan(env, cli, smooth_path, task, gui)


def _execute_flight_plan(env, cli: int, waypoints: List[Tuple[float, float, float]],
                        task: MapTask, gui: bool) -> List[RPMCmd]:
    wps = [np.array(wp, dtype=float) for wp in waypoints]
    wp_idx = 0

    if gui:
        frames_per_cam = max(1, int(round(1.0 / (task.sim_dt * CAM_HZ))))
        step_counter = 0

    t_sim = 0.0
    hover_elapsed = 0.0
    stable_landing_time = 0.0
    extra_counter = 0
    rpm_log: List[RPMCmd] = []

    goal = np.array(task.goal, dtype=float)

    while t_sim < task.horizon:
        if wp_idx < len(wps):
            target = wps[wp_idx]
        else:
            target = wps[-1]

        obs, *_ = env.step(target.reshape(1, 3))
        pos = obs[0, :3]

        if gui and step_counter % frames_per_cam == 0:
            track_drone(cli=cli, drone_id=env.DRONE_IDS[0])

        _record_cmd(rpm_log, env.last_clipped_action[0], t_sim)

        dist_to_target = np.linalg.norm(pos - target)

        if wp_idx < len(wps) - 1:
            if dist_to_target < GOAL_TOL:
                wp_idx += 1
        else:
            if PLATFORM:
                horizontal_distance = np.linalg.norm(pos[:2] - goal[:2])
                vertical_distance = abs(pos[2] - goal[2])

                tao_logo_radius = LANDING_PLATFORM_RADIUS * 0.8 * 1.06
                on_platform = (horizontal_distance < tao_logo_radius and
                              vertical_distance < 0.3 and
                              pos[2] >= goal[2] - 0.1)

                if on_platform:
                    stable_landing_time += task.sim_dt
                    if stable_landing_time >= STABLE_LANDING_SEC:
                        break
                else:
                    stable_landing_time = 0.0

            else:
                if dist_to_target < GOAL_TOL:
                    hover_elapsed += task.sim_dt
                    if hover_elapsed >= HOVER_SEC + 2:
                        extra_counter += 1
                        if extra_counter >= int(1.0 / task.sim_dt):
                            break
                else:
                    hover_elapsed = 0.0

        t_sim += task.sim_dt
        if gui:
            time.sleep(task.sim_dt)
            step_counter += 1

    if not gui:
        env.close()

    return rpm_log


def _record_cmd(buffer: List[RPMCmd], rpm_vec: Sequence[float], t: float) -> None:
    rpm_tuple: Tuple[float, float, float, float] = tuple(float(x) for x in rpm_vec)
    buffer.append(RPMCmd(t=t, rpm=rpm_tuple))

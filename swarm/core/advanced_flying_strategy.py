
from __future__ import annotations

import time
import math
import heapq
from typing import List, Sequence, Tuple, Optional, Set, Dict
from dataclasses import dataclass

import numpy as np
import pybullet as p

from swarm.utils.gui_isolation import run_isolated
from swarm.utils.env_factory import make_env
from swarm.protocol import MapTask, RPMCmd
from swarm.core.drone import track_drone


from swarm.constants import (
    SAFE_Z,
    GOAL_TOL,
    HOVER_SEC,
    CAM_HZ,
    LANDING_PLATFORM_RADIUS,
    STABLE_LANDING_SEC,
    PLATFORM,
    WORLD_RANGE,
)

GRID_RESOLUTION = 0.3
SAFETY_MARGIN = 2.5
MAX_PLANNING_TIME = 8.0
WAYPOINT_SPACING = 0.8
ENERGY_WEIGHT = 0.2
TIME_WEIGHT = 0.8
MIN_FLIGHT_HEIGHT = 3.0
MAX_FLIGHT_HEIGHT = 12.0
COLLISION_BUFFER = 3.0
PRECISION_THRESHOLD = 0.1
MAX_RETRIES = 5


@dataclass
class Obstacle:
    x: float
    y: float
    z: float
    radius: float
    height: float
    shape_type: str

@dataclass
class PathNode:
    x: float
    y: float
    z: float
    g_cost: float = float('inf')
    h_cost: float = 0.0
    f_cost: float = float('inf')
    parent: Optional['PathNode'] = None

    def __hash__(self):
        return hash((round(self.x/GRID_RESOLUTION), round(self.y/GRID_RESOLUTION), round(self.z/GRID_RESOLUTION)))

    def __eq__(self, other):
        if not isinstance(other, PathNode):
            return False
        return (abs(self.x - other.x) < GRID_RESOLUTION/2 and
                abs(self.y - other.y) < GRID_RESOLUTION/2 and
                abs(self.z - other.z) < GRID_RESOLUTION/2)

    def __lt__(self, other):
        if not isinstance(other, PathNode):
            return NotImplemented
        return self.f_cost < other.f_cost

class AdvancedPathPlanner:

    def __init__(self, obstacles: List[Obstacle], world_bounds: Tuple[float, float, float, float]):
        self.obstacles = obstacles
        self.x_min, self.x_max, self.y_min, self.y_max = world_bounds
        self.obstacle_grid = self._build_obstacle_grid()

    def _build_obstacle_grid(self) -> Set[Tuple[int, int, int]]:
        occupied = set()

        for obs in self.obstacles:
            expanded_radius = obs.radius + SAFETY_MARGIN + COLLISION_BUFFER
            expanded_height = obs.height + SAFETY_MARGIN * 2

            x_start = int((obs.x - expanded_radius) / GRID_RESOLUTION) - 2
            x_end = int((obs.x + expanded_radius) / GRID_RESOLUTION) + 3
            y_start = int((obs.y - expanded_radius) / GRID_RESOLUTION) - 2
            y_end = int((obs.y + expanded_radius) / GRID_RESOLUTION) + 3
            z_start = int((obs.z - expanded_height/2) / GRID_RESOLUTION) - 1
            z_end = int((obs.z + expanded_height/2) / GRID_RESOLUTION) + 2

            for x_idx in range(x_start, x_end):
                for y_idx in range(y_start, y_end):
                    for z_idx in range(z_start, z_end):
                        x_world = x_idx * GRID_RESOLUTION
                        y_world = y_idx * GRID_RESOLUTION
                        z_world = z_idx * GRID_RESOLUTION

                        if self._point_in_expanded_obstacle(x_world, y_world, z_world, obs):
                            occupied.add((x_idx, y_idx, z_idx))

        return occupied
    
    def _point_in_obstacle(self, x: float, y: float, z: float, obs: Obstacle) -> bool:
        horiz_dist = math.sqrt((x - obs.x)**2 + (y - obs.y)**2)
        if horiz_dist > obs.radius + SAFETY_MARGIN:
            return False

        if z < obs.z - obs.height/2 or z > obs.z + obs.height/2 + SAFETY_MARGIN:
            return False

        return True

    def _point_in_expanded_obstacle(self, x: float, y: float, z: float, obs: Obstacle) -> bool:
        expanded_radius = obs.radius + SAFETY_MARGIN + COLLISION_BUFFER
        expanded_height = obs.height + SAFETY_MARGIN * 2

        horiz_dist = math.sqrt((x - obs.x)**2 + (y - obs.y)**2)
        if horiz_dist > expanded_radius:
            return False

        if z < obs.z - expanded_height/2 or z > obs.z + expanded_height/2:
            return False

        return True

    def _is_valid_position(self, x: float, y: float, z: float) -> bool:
        if (x < self.x_min or x > self.x_max or
            y < self.y_min or y > self.y_max or
            z < MIN_FLIGHT_HEIGHT or z > MAX_FLIGHT_HEIGHT):
            return False

        x_idx = int(x / GRID_RESOLUTION)
        y_idx = int(y / GRID_RESOLUTION)
        z_idx = int(z / GRID_RESOLUTION)

        return (x_idx, y_idx, z_idx) not in self.obstacle_grid

    def _heuristic(self, node: PathNode, goal: PathNode) -> float:
        dx = node.x - goal.x
        dy = node.y - goal.y
        dz = node.z - goal.z

        distance = math.sqrt(dx*dx + dy*dy + dz*dz)

        altitude_penalty = abs(dz) * 0.5

        return distance + altitude_penalty
    
    def _get_neighbors(self, node: PathNode) -> List[PathNode]:
        neighbors = []

        directions = [
            (1, 0, 0), (-1, 0, 0), (0, 1, 0), (0, -1, 0), (0, 0, 1), (0, 0, -1),
            (1, 1, 0), (1, -1, 0), (-1, 1, 0), (-1, -1, 0),
            (1, 0, 1), (1, 0, -1), (-1, 0, 1), (-1, 0, -1),
            (0, 1, 1), (0, 1, -1), (0, -1, 1), (0, -1, -1),
            (1, 1, 1), (1, 1, -1), (1, -1, 1), (1, -1, -1),
            (-1, 1, 1), (-1, 1, -1), (-1, -1, 1), (-1, -1, -1)
        ]

        for dx, dy, dz in directions:
            new_x = node.x + dx * GRID_RESOLUTION
            new_y = node.y + dy * GRID_RESOLUTION
            new_z = node.z + dz * GRID_RESOLUTION

            if self._is_valid_position(new_x, new_y, new_z):
                neighbors.append(PathNode(new_x, new_y, new_z))

        return neighbors

    def _calculate_movement_cost(self, from_node: PathNode, to_node: PathNode) -> float:
        dx = to_node.x - from_node.x
        dy = to_node.y - from_node.y
        dz = to_node.z - from_node.z

        distance = math.sqrt(dx*dx + dy*dy + dz*dz)

        if dz > 0:
            energy_penalty = dz * 2.0
        else:
            energy_penalty = abs(dz) * 0.5

        time_penalty = distance * 1.0

        return TIME_WEIGHT * time_penalty + ENERGY_WEIGHT * energy_penalty
    
    def find_path(self, start: Tuple[float, float, float],
                  goal: Tuple[float, float, float]) -> Optional[List[Tuple[float, float, float]]]:
        start_node = PathNode(start[0], start[1], start[2], g_cost=0.0)
        goal_node = PathNode(goal[0], goal[1], goal[2])

        start_node.h_cost = self._heuristic(start_node, goal_node)
        start_node.f_cost = start_node.g_cost + start_node.h_cost

        open_set = [start_node]
        closed_set: Set[PathNode] = set()
        node_map: Dict[Tuple[int, int, int], PathNode] = {}

        start_time = time.time()

        while open_set and (time.time() - start_time) < MAX_PLANNING_TIME:
            current = heapq.heappop(open_set)

            if (abs(current.x - goal_node.x) < GRID_RESOLUTION and
                abs(current.y - goal_node.y) < GRID_RESOLUTION and
                abs(current.z - goal_node.z) < GRID_RESOLUTION):
                path = []
                while current:
                    path.append((current.x, current.y, current.z))
                    current = current.parent
                return path[::-1]

            closed_set.add(current)

            for neighbor in self._get_neighbors(current):
                if neighbor in closed_set:
                    continue

                tentative_g = current.g_cost + self._calculate_movement_cost(current, neighbor)

                neighbor_key = (int(neighbor.x/GRID_RESOLUTION),
                               int(neighbor.y/GRID_RESOLUTION),
                               int(neighbor.z/GRID_RESOLUTION))

                if neighbor_key in node_map:
                    existing_neighbor = node_map[neighbor_key]
                    if tentative_g >= existing_neighbor.g_cost:
                        continue
                    neighbor = existing_neighbor
                else:
                    node_map[neighbor_key] = neighbor

                neighbor.g_cost = tentative_g
                neighbor.h_cost = self._heuristic(neighbor, goal_node)
                neighbor.f_cost = neighbor.g_cost + neighbor.h_cost
                neighbor.parent = current

                if neighbor not in open_set:
                    heapq.heappush(open_set, neighbor)

        return None

    def find_path_rrt_star(self, start: Tuple[float, float, float],
                          goal: Tuple[float, float, float]) -> Optional[List[Tuple[float, float, float]]]:
        import random

        class RRTNode:
            def __init__(self, x, y, z, parent=None, cost=0.0):
                self.x, self.y, self.z = x, y, z
                self.parent = parent
                self.cost = cost

        def distance(n1, n2):
            return math.sqrt((n1.x - n2.x)**2 + (n1.y - n2.y)**2 + (n1.z - n2.z)**2)

        def steer(from_node, to_point, max_dist=2.0):
            dist = distance(from_node, RRTNode(*to_point))
            if dist <= max_dist:
                return RRTNode(*to_point)

            ratio = max_dist / dist
            new_x = from_node.x + ratio * (to_point[0] - from_node.x)
            new_y = from_node.y + ratio * (to_point[1] - from_node.y)
            new_z = from_node.z + ratio * (to_point[2] - from_node.z)
            return RRTNode(new_x, new_y, new_z)

        def is_collision_free(from_node, to_node):
            steps = int(distance(from_node, to_node) / (GRID_RESOLUTION / 2)) + 1
            for i in range(steps + 1):
                t = i / steps if steps > 0 else 0
                x = from_node.x + t * (to_node.x - from_node.x)
                y = from_node.y + t * (to_node.y - from_node.y)
                z = from_node.z + t * (to_node.z - from_node.z)

                if not self._is_valid_position(x, y, z):
                    return False
            return True

        start_node = RRTNode(*start)
        goal_node = RRTNode(*goal)
        nodes = [start_node]

        max_iterations = 2000
        goal_sample_rate = 0.1
        search_radius = 3.0

        for _ in range(max_iterations):
            if random.random() < goal_sample_rate:
                rand_point = goal
            else:
                rand_point = (
                    random.uniform(self.x_min, self.x_max),
                    random.uniform(self.y_min, self.y_max),
                    random.uniform(MIN_FLIGHT_HEIGHT, MAX_FLIGHT_HEIGHT)
                )

            nearest_node = min(nodes, key=lambda n: distance(n, RRTNode(*rand_point)))
            new_node = steer(nearest_node, rand_point)

            if is_collision_free(nearest_node, new_node):
                new_node.parent = nearest_node
                new_node.cost = nearest_node.cost + distance(nearest_node, new_node)

                near_nodes = [n for n in nodes if distance(n, new_node) < search_radius]

                for near_node in near_nodes:
                    if (is_collision_free(near_node, new_node) and
                        near_node.cost + distance(near_node, new_node) < new_node.cost):
                        new_node.parent = near_node
                        new_node.cost = near_node.cost + distance(near_node, new_node)

                nodes.append(new_node)

                for near_node in near_nodes:
                    if (is_collision_free(new_node, near_node) and
                        new_node.cost + distance(new_node, near_node) < near_node.cost):
                        near_node.parent = new_node
                        near_node.cost = new_node.cost + distance(new_node, near_node)

                if distance(new_node, goal_node) < PRECISION_THRESHOLD:
                    path = []
                    current = new_node
                    while current:
                        path.append((current.x, current.y, current.z))
                        current = current.parent
                    return path[::-1]

        return None


def extract_obstacles_from_environment(cli: int) -> List[Obstacle]:
    obstacles = []

    num_bodies = p.getNumBodies(physicsClientId=cli)

    for body_id in range(num_bodies):
        if body_id <= 1:
            continue

        try:
            pos, _ = p.getBasePositionAndOrientation(body_id, physicsClientId=cli)

            collision_info = p.getCollisionShapeData(body_id, -1, physicsClientId=cli)

            if not collision_info:
                continue

            for shape_data in collision_info:
                shape_type = shape_data[2]
                dimensions = shape_data[3]
                local_pos = shape_data[5]

                world_x = pos[0] + local_pos[0]
                world_y = pos[1] + local_pos[1]
                world_z = pos[2] + local_pos[2]

                if shape_type == p.GEOM_BOX:
                    radius = math.sqrt(dimensions[0]**2 + dimensions[1]**2)
                    height = dimensions[2] * 2
                    obstacles.append(Obstacle(world_x, world_y, world_z, radius, height, 'box'))

                elif shape_type == p.GEOM_CYLINDER:
                    radius = dimensions[0]
                    height = dimensions[1]
                    obstacles.append(Obstacle(world_x, world_y, world_z, radius, height, 'cylinder'))

                elif shape_type == p.GEOM_SPHERE:
                    radius = dimensions[0]
                    height = radius * 2
                    obstacles.append(Obstacle(world_x, world_y, world_z, radius, height, 'sphere'))

        except Exception:
            continue

    return obstacles


def optimize_waypoints(path: List[Tuple[float, float, float]],
                      obstacles: List[Obstacle]) -> List[Tuple[float, float, float]]:
    if len(path) <= 2:
        return path

    optimized = [path[0]]

    i = 0
    while i < len(path) - 1:
        farthest_reachable = i + 1

        for j in range(i + 2, len(path)):
            if _is_line_clear(path[i], path[j], obstacles):
                farthest_reachable = j
            else:
                break

        if farthest_reachable < len(path):
            optimized.append(path[farthest_reachable])

        i = farthest_reachable

    if optimized[-1] != path[-1]:
        optimized.append(path[-1])

    return optimized


def _is_line_clear(start: Tuple[float, float, float],
                   end: Tuple[float, float, float],
                   obstacles: List[Obstacle]) -> bool:
    num_samples = int(np.linalg.norm(np.array(end) - np.array(start)) / (GRID_RESOLUTION / 2)) + 1

    for i in range(num_samples + 1):
        t = i / num_samples if num_samples > 0 else 0
        point = (
            start[0] + t * (end[0] - start[0]),
            start[1] + t * (end[1] - start[1]),
            start[2] + t * (end[2] - start[2])
        )

        for obs in obstacles:
            if _point_in_obstacle_simple(point[0], point[1], point[2], obs):
                return False

    return True


def _point_in_obstacle_simple(x: float, y: float, z: float, obs: Obstacle) -> bool:
    horiz_dist = math.sqrt((x - obs.x)**2 + (y - obs.y)**2)
    if horiz_dist > obs.radius + SAFETY_MARGIN:
        return False

    if z < obs.z - obs.height/2 or z > obs.z + obs.height/2 + SAFETY_MARGIN:
        return False

    return True


def smooth_trajectory(waypoints: List[Tuple[float, float, float]],
                     smoothing_factor: float = 0.3) -> List[Tuple[float, float, float]]:
    if len(waypoints) <= 2:
        return waypoints

    smoothed = [waypoints[0]]

    for i in range(1, len(waypoints) - 1):
        prev_wp = np.array(waypoints[i-1])
        curr_wp = np.array(waypoints[i])
        next_wp = np.array(waypoints[i+1])

        smoothed_point = (
            (1 - smoothing_factor) * curr_wp +
            smoothing_factor * 0.5 * (prev_wp + next_wp)
        )

        smoothed.append(tuple(smoothed_point))

    smoothed.append(waypoints[-1])
    return smoothed


def optimize_waypoints_ultra_safe(path: List[Tuple[float, float, float]],
                                 obstacles: List[Obstacle]) -> List[Tuple[float, float, float]]:
    if len(path) <= 2:
        return path

    optimized = [path[0]]

    i = 0
    while i < len(path) - 1:
        farthest_reachable = i + 1

        for j in range(i + 2, len(path)):
            if _is_line_ultra_safe(path[i], path[j], obstacles):
                farthest_reachable = j
            else:
                break

        if farthest_reachable < len(path):
            optimized.append(path[farthest_reachable])

        i = farthest_reachable

    if optimized[-1] != path[-1]:
        optimized.append(path[-1])

    return optimized


def _is_line_ultra_safe(start: Tuple[float, float, float],
                       end: Tuple[float, float, float],
                       obstacles: List[Obstacle]) -> bool:
    num_samples = int(np.linalg.norm(np.array(end) - np.array(start)) / (GRID_RESOLUTION / 4)) + 1

    for i in range(num_samples + 1):
        t = i / num_samples if num_samples > 0 else 0
        point = (
            start[0] + t * (end[0] - start[0]),
            start[1] + t * (end[1] - start[1]),
            start[2] + t * (end[2] - start[2])
        )

        for obs in obstacles:
            expanded_radius = obs.radius + SAFETY_MARGIN + COLLISION_BUFFER
            expanded_height = obs.height + SAFETY_MARGIN * 2

            horiz_dist = math.sqrt((point[0] - obs.x)**2 + (point[1] - obs.y)**2)
            if horiz_dist <= expanded_radius:
                if obs.z - expanded_height/2 <= point[2] <= obs.z + expanded_height/2:
                    return False

    return True


def smooth_trajectory_precision(waypoints: List[Tuple[float, float, float]],
                               smoothing_factor: float = 0.15) -> List[Tuple[float, float, float]]:
    if len(waypoints) <= 2:
        return waypoints

    smoothed = [waypoints[0]]

    for i in range(1, len(waypoints) - 1):
        prev_wp = np.array(waypoints[i-1])
        curr_wp = np.array(waypoints[i])
        next_wp = np.array(waypoints[i+1])

        smoothed_point = (
            (1 - smoothing_factor) * curr_wp +
            smoothing_factor * 0.25 * (prev_wp + next_wp)
        )

        smoothed.append(tuple(smoothed_point))

    smoothed.append(waypoints[-1])

    intermediate_points = []
    for i in range(len(smoothed) - 1):
        current = np.array(smoothed[i])
        next_point = np.array(smoothed[i + 1])

        intermediate_points.append(tuple(current))

        distance = np.linalg.norm(next_point - current)
        if distance > WAYPOINT_SPACING:
            num_intermediates = int(distance / WAYPOINT_SPACING)
            for j in range(1, num_intermediates + 1):
                t = j / (num_intermediates + 1)
                intermediate = current + t * (next_point - current)
                intermediate_points.append(tuple(intermediate))

    intermediate_points.append(smoothed[-1])
    return intermediate_points


def advanced_flying_strategy(task: MapTask, *, gui: bool = False) -> List[RPMCmd]:
    print("🚁 Advanced flying strategy called!")
    return run_isolated(_advanced_flying_strategy_impl, task, gui=gui)


def _advanced_flying_strategy_impl(task: MapTask, *, gui: bool = False) -> List[RPMCmd]:
    print("🎯 Conservative high-success strategy starting...")
    env = make_env(task, gui=gui, raw_rpm=False)
    cli = env.getPyBulletClient()

    obstacles = extract_obstacles_from_environment(cli)
    print(f"🔍 Detected {len(obstacles)} obstacles")

    start_xyz = np.array(task.start, dtype=float)
    goal_xyz = np.array(task.goal, dtype=float)

    max_obstacle_height = 0.0
    if obstacles:
        max_obstacle_height = max(obs.z + obs.height/2 for obs in obstacles)

    conservative_altitude = max(
        SAFE_Z + 5.0,
        max_obstacle_height + 8.0,
        start_xyz[2] + 3.0,
        goal_xyz[2] + 3.0,
        15.0
    )

    print(f"🚁 Using conservative altitude: {conservative_altitude:.1f}m (max obstacle: {max_obstacle_height:.1f}m)")

    strategy_type = _select_strategy(obstacles, start_xyz, goal_xyz, conservative_altitude)

    if strategy_type == "ULTRA_CONSERVATIVE":
        full_path = _create_ultra_conservative_path(start_xyz, goal_xyz, conservative_altitude)
        print("🛡️  Using ULTRA_CONSERVATIVE strategy")
    elif strategy_type == "HIGH_ALTITUDE":
        full_path = _create_high_altitude_path(start_xyz, goal_xyz, conservative_altitude, obstacles)
        print("🚁 Using HIGH_ALTITUDE strategy")
    else:
        world_bounds = (-WORLD_RANGE, WORLD_RANGE, -WORLD_RANGE, WORLD_RANGE)
        planner = AdvancedPathPlanner(obstacles, world_bounds)
        full_path = _create_planned_path(planner, start_xyz, goal_xyz, conservative_altitude)
        print("🎯 Using PLANNED strategy")

    if not full_path:
        print("🆘 All strategies failed, using emergency path")
        full_path = _create_emergency_path(start_xyz, goal_xyz)

    final_path = _validate_and_adjust_path(full_path, obstacles)

    print(f"🎯 Final path has {len(final_path)} waypoints")
    return _execute_flight_plan_precision(env, cli, final_path, task, gui)


def _select_strategy(obstacles: List[Obstacle], start_xyz: np.ndarray,
                    goal_xyz: np.ndarray, conservative_altitude: float) -> str:
    return "ULTRA_CONSERVATIVE"


def _create_ultra_conservative_path(start_xyz: np.ndarray, goal_xyz: np.ndarray,
                                   altitude: float) -> List[Tuple[float, float, float]]:
    stratosphere_altitude = 50.0

    horizontal_distance = np.linalg.norm(goal_xyz[:2] - start_xyz[:2])

    path = [
        (start_xyz[0], start_xyz[1], start_xyz[2]),
        (start_xyz[0], start_xyz[1], stratosphere_altitude)
    ]

    if horizontal_distance > 10.0:
        num_segments = max(5, int(horizontal_distance / 5.0))
        for i in range(1, num_segments):
            t = i / num_segments
            intermediate_x = start_xyz[0] + t * (goal_xyz[0] - start_xyz[0])
            intermediate_y = start_xyz[1] + t * (goal_xyz[1] - start_xyz[1])
            path.append((intermediate_x, intermediate_y, stratosphere_altitude))

    high_descent_altitude = max(goal_xyz[2] + 10.0, 20.0)
    mid_descent_altitude = max(goal_xyz[2] + 5.0, 10.0)

    path.extend([
        (goal_xyz[0], goal_xyz[1], stratosphere_altitude),
        (goal_xyz[0], goal_xyz[1], high_descent_altitude),
        (goal_xyz[0], goal_xyz[1], mid_descent_altitude),
        (goal_xyz[0], goal_xyz[1], goal_xyz[2])
    ])

    return path


def _create_high_altitude_path(start_xyz: np.ndarray, goal_xyz: np.ndarray,
                              altitude: float, obstacles: List[Obstacle]) -> List[Tuple[float, float, float]]:
    path = [
        (start_xyz[0], start_xyz[1], start_xyz[2]),
        (start_xyz[0], start_xyz[1], altitude)
    ]

    horizontal_distance = np.linalg.norm(goal_xyz[:2] - start_xyz[:2])
    if horizontal_distance > 15.0:
        mid_x = (start_xyz[0] + goal_xyz[0]) / 2
        mid_y = (start_xyz[1] + goal_xyz[1]) / 2
        path.append((mid_x, mid_y, altitude))

    path.extend([
        (goal_xyz[0], goal_xyz[1], altitude),
        (goal_xyz[0], goal_xyz[1], goal_xyz[2])
    ])

    return path


def _create_planned_path(planner: AdvancedPathPlanner, start_xyz: np.ndarray,
                        goal_xyz: np.ndarray, altitude: float) -> Optional[List[Tuple[float, float, float]]]:
    start_point = (start_xyz[0], start_xyz[1], altitude)
    intermediate_point = (goal_xyz[0], goal_xyz[1], altitude)
    final_point = (goal_xyz[0], goal_xyz[1], goal_xyz[2])

    path_to_intermediate = planner.find_path(start_point, intermediate_point)
    if not path_to_intermediate:
        path_to_intermediate = planner.find_path_rrt_star(start_point, intermediate_point)

    if path_to_intermediate:
        path_to_goal = planner.find_path(intermediate_point, final_point)
        if not path_to_goal:
            path_to_goal = [final_point]

        return path_to_intermediate + path_to_goal[1:]

    return None


def _create_emergency_path(start_xyz: np.ndarray, goal_xyz: np.ndarray) -> List[Tuple[float, float, float]]:
    emergency_altitude = 20.0

    return [
        (start_xyz[0], start_xyz[1], start_xyz[2]),
        (start_xyz[0], start_xyz[1], emergency_altitude),
        (goal_xyz[0], goal_xyz[1], emergency_altitude),
        (goal_xyz[0], goal_xyz[1], goal_xyz[2])
    ]


def _validate_and_adjust_path(path: List[Tuple[float, float, float]],
                             obstacles: List[Obstacle]) -> List[Tuple[float, float, float]]:
    if not path:
        return path

    validated_path = [path[0]]

    for i in range(1, len(path)):
        current = path[i-1]
        next_point = path[i]

        if _is_line_ultra_safe(current, next_point, obstacles):
            validated_path.append(next_point)
        else:
            safe_altitude = max(20.0, next_point[2] + 5.0)
            intermediate = (next_point[0], next_point[1], safe_altitude)
            validated_path.extend([intermediate, next_point])

    return smooth_trajectory_precision(validated_path, smoothing_factor=0.1)


def _execute_flight_plan_precision(env, cli: int, waypoints: List[Tuple[float, float, float]],
                                  task: MapTask, gui: bool) -> List[RPMCmd]:
    wps = [np.array(wp, dtype=float) for wp in waypoints]
    wp_idx = 0

    if gui:
        frames_per_cam = max(1, int(round(1.0 / (task.sim_dt * CAM_HZ))))
        step_counter = 0

    t_sim = 0.0
    hover_elapsed = 0.0
    stable_landing_time = 0.0
    extra_counter = 0
    rpm_log: List[RPMCmd] = []

    goal = np.array(task.goal, dtype=float)
    precision_tolerance = GOAL_TOL * 0.3

    consecutive_stable_steps = 0
    required_stable_steps = int(STABLE_LANDING_SEC / task.sim_dt) if PLATFORM else int(HOVER_SEC / task.sim_dt)

    velocity_damping = 0.8

    while t_sim < task.horizon:
        if wp_idx < len(wps):
            target = wps[wp_idx]
        else:
            target = wps[-1]

        obs, *_ = env.step(target.reshape(1, 3))
        pos = obs[0, :3]

        if gui and step_counter % frames_per_cam == 0:
            track_drone(cli=cli, drone_id=env.DRONE_IDS[0])

        _record_cmd(rpm_log, env.last_clipped_action[0], t_sim)

        dist_to_target = np.linalg.norm(pos - target)

        if wp_idx < len(wps) - 1:
            if dist_to_target < precision_tolerance:
                wp_idx += 1
                consecutive_stable_steps = 0
        else:
            if PLATFORM:
                horizontal_distance = np.linalg.norm(pos[:2] - goal[:2])
                vertical_distance = abs(pos[2] - goal[2])

                tao_logo_radius = LANDING_PLATFORM_RADIUS * 0.8 * 1.06 * 0.7
                on_platform = (horizontal_distance < tao_logo_radius and
                              vertical_distance < 0.15 and
                              pos[2] >= goal[2] - 0.03)

                if on_platform:
                    stable_landing_time += task.sim_dt
                    consecutive_stable_steps += 1

                    if stable_landing_time >= STABLE_LANDING_SEC and consecutive_stable_steps >= required_stable_steps:
                        print(f"✅ Stable landing achieved after {stable_landing_time:.2f}s")
                        break
                else:
                    stable_landing_time = 0.0
                    consecutive_stable_steps = 0

            else:
                if dist_to_target < precision_tolerance:
                    hover_elapsed += task.sim_dt
                    consecutive_stable_steps += 1

                    if hover_elapsed >= HOVER_SEC and consecutive_stable_steps >= required_stable_steps:
                        extra_counter += 1
                        if extra_counter >= int(1.0 / task.sim_dt):
                            print(f"✅ Hover completed after {hover_elapsed:.2f}s")
                            break
                else:
                    hover_elapsed = 0.0
                    consecutive_stable_steps = 0

        t_sim += task.sim_dt
        if gui:
            time.sleep(task.sim_dt)
            step_counter += 1

    if not gui:
        env.close()

    print(f"🏁 Flight completed: t={t_sim:.2f}s, waypoints_reached={wp_idx}/{len(wps)}")
    return rpm_log


def _execute_flight_plan(env, cli: int, waypoints: List[Tuple[float, float, float]],
                        task: MapTask, gui: bool) -> List[RPMCmd]:
    wps = [np.array(wp, dtype=float) for wp in waypoints]
    wp_idx = 0

    if gui:
        frames_per_cam = max(1, int(round(1.0 / (task.sim_dt * CAM_HZ))))
        step_counter = 0

    t_sim = 0.0
    hover_elapsed = 0.0
    stable_landing_time = 0.0
    extra_counter = 0
    rpm_log: List[RPMCmd] = []

    goal = np.array(task.goal, dtype=float)

    while t_sim < task.horizon:
        if wp_idx < len(wps):
            target = wps[wp_idx]
        else:
            target = wps[-1]

        obs, *_ = env.step(target.reshape(1, 3))
        pos = obs[0, :3]

        if gui and step_counter % frames_per_cam == 0:
            track_drone(cli=cli, drone_id=env.DRONE_IDS[0])

        _record_cmd(rpm_log, env.last_clipped_action[0], t_sim)

        dist_to_target = np.linalg.norm(pos - target)

        if wp_idx < len(wps) - 1:
            if dist_to_target < GOAL_TOL:
                wp_idx += 1
        else:
            if PLATFORM:
                horizontal_distance = np.linalg.norm(pos[:2] - goal[:2])
                vertical_distance = abs(pos[2] - goal[2])

                tao_logo_radius = LANDING_PLATFORM_RADIUS * 0.8 * 1.06
                on_platform = (horizontal_distance < tao_logo_radius and
                              vertical_distance < 0.3 and
                              pos[2] >= goal[2] - 0.1)

                if on_platform:
                    stable_landing_time += task.sim_dt
                    if stable_landing_time >= STABLE_LANDING_SEC:
                        break
                else:
                    stable_landing_time = 0.0

            else:
                if dist_to_target < GOAL_TOL:
                    hover_elapsed += task.sim_dt
                    if hover_elapsed >= HOVER_SEC + 2:
                        extra_counter += 1
                        if extra_counter >= int(1.0 / task.sim_dt):
                            break
                else:
                    hover_elapsed = 0.0

        t_sim += task.sim_dt
        if gui:
            time.sleep(task.sim_dt)
            step_counter += 1

    if not gui:
        env.close()

    return rpm_log


def _record_cmd(buffer: List[RPMCmd], rpm_vec: Sequence[float], t: float) -> None:
    rpm_tuple: Tuple[float, float, float, float] = tuple(float(x) for x in rpm_vec)
    buffer.append(RPMCmd(t=t, rpm=rpm_tuple))
